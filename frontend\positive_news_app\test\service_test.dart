import 'package:flutter_test/flutter_test.dart';
import 'package:positive_news_app/models/news_model.dart';
import 'package:positive_news_app/services/favorites_service.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:positive_news_app/services/api_service.dart';

@GenerateMocks([ApiService])
import 'service_test.mocks.dart';

void main() {
  group('FavoritesService Tests', () {
    late FavoritesService favoritesService;
    
    setUp(() {
      favoritesService = FavoritesService();
    });
    
    test('Favorit hinzufügen und prüfen', () {
      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        published: DateTime.now(),
        llmPositive: 0.95,
        hasSimilar: false,
      );
      
      favoritesService.addFavorite(news);
      
      expect(favoritesService.isFavorite(news), true);
      expect(favoritesService.favorites.length, 1);
      expect(favoritesService.favorites[0].entryId, 'test_id');
    });
    
    test('Favorit entfernen', () {
      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        published: DateTime.now(),
        llmPositive: 0.95,
        hasSimilar: false,
      );
      
      favoritesService.addFavorite(news);
      favoritesService.removeFavorite(news);
      
      expect(favoritesService.isFavorite(news), false);
      expect(favoritesService.favorites.length, 0);
    });
    
    test('Alle Favoriten löschen', () {
      final news1 = EntryWithSource(
        entryId: 'test_id_1',
        title: 'Test Nachricht 1',
        link: 'https://example.com/1',
        source: 'test_source',
        published: DateTime.now(),
        llmPositive: 0.95,
        hasSimilar: false,
      );
      
      final news2 = EntryWithSource(
        entryId: 'test_id_2',
        title: 'Test Nachricht 2',
        link: 'https://example.com/2',
        source: 'test_source',
        published: DateTime.now(),
        llmPositive: 0.92,
        hasSimilar: false,
      );
      
      favoritesService.addFavorite(news1);
      favoritesService.addFavorite(news2);
      
      favoritesService.clearFavorites();
      
      expect(favoritesService.favorites.length, 0);
    });
  });
  
  group('ApiService Tests mit Mocks', () {
    late MockApiService mockApiService;
    
    setUp(() {
      mockApiService = MockApiService();
    });
    
    test('getNewsByCategories liefert erwartete Daten', () async {
      final categoryNews = CategoryNews(
        category: 'Politik',
        categoryCode: 'POL',
        news: [
          EntryWithSource(
            entryId: 'test_id_1',
            title: 'Test Nachricht 1',
            link: 'https://example.com/1',
            source: 'test_source',
            published: DateTime.now(),
            llmPositive: 0.95,
            hasSimilar: false,
          ),
        ],
      );
      
      final newsResponse = NewsResponse(
        categories: [categoryNews],
      );
      
      when(mockApiService.getNewsByCategories()).thenAnswer((_) async => newsResponse);
      
      final result = await mockApiService.getNewsByCategories();
      
      expect(result.categories.length, 1);
      expect(result.categories[0].category, 'POL');
      expect(result.categories[0].news.length, 1);
      expect(result.categories[0].news[0].title, 'Test Nachricht 1');
      
      verify(mockApiService.getNewsByCategories()).called(1);
    });
    
    test('getSimilarNews liefert erwartete Daten', () async {
      final originalNews = EntryWithSource(
        entryId: 'test_id_1',
        title: 'Original Nachricht',
        link: 'https://example.com/1',
        source: 'test_source',
        published: DateTime.now(),
        llmPositive: 0.95,
        hasSimilar: false,
      );
      
      final similarNews = EntryWithSource(
        entryId: 'test_id_2',
        title: 'Ähnliche Nachricht',
        link: 'https://example.com/2',
        source: 'other_source',
        published: DateTime.now(),
        llmPositive: 0.92,
        hasSimilar: false,
      );
      
      final similarNewsResponse = SimilarNewsResponse(
        original: originalNews,
        similarSources: [similarNews],
      );
      
      when(mockApiService.getSimilarNews('test_id_1')).thenAnswer((_) async => similarNewsResponse);
      
      final result = await mockApiService.getSimilarNews('test_id_1');
      
      expect(result.original.title, 'Original Nachricht');
      expect(result.similarSources.length, 1);
      expect(result.similarSources[0].title, 'Ähnliche Nachricht');
      
      verify(mockApiService.getSimilarNews('test_id_1')).called(1);
    });
  });
}
