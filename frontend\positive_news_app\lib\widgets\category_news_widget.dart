import 'package:flutter/material.dart';
import 'package:positive_news_app/models/news_model.dart';
import 'package:positive_news_app/widgets/responsive_news_grid.dart';
import 'package:positive_news_app/screens/category/category_view_screen.dart';

class CategoryNewsWidget extends StatelessWidget {
  final CategoryNews categoryNews;

  const CategoryNewsWidget({
    super.key,
    required this.categoryNews,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                categoryNews.category,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              TextButton(
                onPressed: () {
                  // Zur Kategoriedetailansicht navigieren
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CategoryViewScreen(
                        categoryCode: categoryNews.categoryCode,
                        categoryName: categoryNews.category,
                      ),
                    ),
                  );
                },
                child: const Text('Mehr anzeigen'),
              ),
            ],
          ),
        ),
        ResponsiveNewsGrid(
          newsList: categoryNews.news,
          showFavoriteButton: true,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
        ),
        const Divider(height: 32, thickness: 1),
      ],
    );
  }
}
