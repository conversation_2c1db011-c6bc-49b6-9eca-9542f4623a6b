import sqlalchemy
from datetime import datetime
import os
import sys
from sqlalchemy import text

# Ensure project root is in sys.path for config import
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
from backend.app.core.config import settings

# --- CONFIGURATION ---
ORACLE_URL = settings.DATABASE_URL
CUTOFF_DATE = "2025-04-01"  # YYYY-MM-DD

# --- DELETE FROM ORACLE ---
oracle_engine = sqlalchemy.create_engine(ORACLE_URL)
with oracle_engine.begin() as conn:
    result = conn.execute(
        text(f"DELETE FROM entries WHERE published < TO_DATE('{CUTOFF_DATE}', 'YYYY-MM-DD')")
    )
    print(f"Deleted {result.rowcount} entries from Oracle where published < {CUTOFF_DATE}.")
    # Enable row movement
    conn.execute(text("ALTER TABLE entries ENABLE ROW MOVEMENT"))
    print("Enabled row movement on entries table.")
    # Shrink space
    conn.execute(text("ALTER TABLE entries SHRINK SPACE"))
    print("Shrunk space on entries table.")
    
print("Done.")
