from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, case
from backend.app.core.config import settings
from backend.app.models.models import Entry, Source, Category

print("crud.py has been (re)loaded")


def get_positive_news(
    db: Session,
    skip: int = 0,
    limit: int = 10,
    min_positive_score: float = settings.NEWS_SENTIMENT_THRESHOLD,
    include_ads: bool = False,
    hours_back: int = 24,
    category: Optional[str] = None,
    search: Optional[str] = None
) -> Tuple[List[Dict], int]:
    """
    Fetches positive news from the database, enriched with source names,
    category names, and 'has_similar' flags.
    """
    # Time filter: Only news from the last X hours
    time_threshold = datetime.now(timezone.utc) - timedelta(hours=hours_back)

    # Subquery to get the most recent entry per dup_entry_id, with a tie-breaker on entry_id
    latest_entries_subquery = (
        db.query(
            Entry.dup_entry_id,
            func.max(Entry.published).label("latest_published"),
            func.min(Entry.entry_id).label("tie_breaker_entry_id")  # Use the smallest entry_id as a tie-breaker
        )
        .filter(
            Entry.llm_positive >= min_positive_score,
            Entry.published >= time_threshold,
            Entry.iptc_newscode != None,  # Exclude entries without a category
            Entry.dup_entry_id != None   # Exclude entries without a duplicate group
        )
        .group_by(Entry.dup_entry_id)
        .subquery()
    )

    # Main query to fetch positive news with distinct dup_entry_ids
    query = (
        db.query(
            Entry.entry_id,
            Entry.iptc_newscode,
            Entry.source,
            Entry.published,
            Entry.llm_positive,
            Entry.dup_entry_id,
            Entry.title,
            Entry.link,
            Entry.description,
            Entry.description_auto_generated,
            # Don't select preview_img, just check if it exists
            case((Entry.preview_img != None, True), else_=False).label("has_image"),
            Category.category.label("category_name"),
            Source.name.label("source_name")
        )
        .join(
            latest_entries_subquery,
            and_(
                Entry.dup_entry_id == latest_entries_subquery.c.dup_entry_id,
                Entry.published == latest_entries_subquery.c.latest_published,
                Entry.entry_id == latest_entries_subquery.c.tie_breaker_entry_id  # Ensure tie-breaker is applied
            )
        )
        .join(Category, Entry.iptc_newscode == Category.iptc_newscode, isouter=True)
        .join(Source, Entry.source == Source.source, isouter=True)
        .filter(
            Entry.llm_positive >= min_positive_score,
            Entry.published >= time_threshold
        )
    )

    # Exclude ads if not explicitly included
    if not include_ads:
        query = query.filter(Entry.llm_is_ad < 0.5)

    # Filter by category if specified
    if category:
        query = query.filter(Entry.iptc_newscode == category)
        
    # Add search filter if specified
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Entry.title.ilike(search_term),
                Entry.description.ilike(search_term)
            )
        )

    # Total count of results for pagination
    total = query.count()

    # Apply pagination
    entries = query.order_by(Entry.published.desc()).offset(skip).limit(limit).all()

    # Precompute 'has_similar' flags for all entries
    dup_entry_ids = [entry.dup_entry_id for entry in entries]
    similar_counts = (
        db.query(
            Entry.dup_entry_id,
            func.count(func.distinct(Entry.source)).label("count")  # Count distinct sources
        )
        .filter(Entry.dup_entry_id.in_(dup_entry_ids))  # Filter by relevant dup_entry_ids
        .group_by(Entry.dup_entry_id)
        .all()
    )

    # Create a map of dup_entry_id to whether it has similar entries
    similar_map = {dup_id: count > 1 for dup_id, count in similar_counts}

    # Enrich entries with 'has_similar' flags
    enriched_entries = [
        {
            **{k: v for k, v in entry._asdict().items() if not k.startswith('_')},
            "has_similar": similar_map.get(entry.dup_entry_id, False)
            # has_image is already included from the query
        }
        for entry in entries
    ]

    return enriched_entries, total


def get_news_by_category(
    db: Session,
    min_positive_score: float = settings.NEWS_SENTIMENT_THRESHOLD,
    hours_back: int = 24,
    limit_per_category: int = 3
) -> Dict[str, Dict]:
    """
    Fetches positive news grouped by categories, with a limit of top entries per category.
    Enriches entries with source names, category names, and 'has_similar' flags.
    """
    # Time filter: Only news from the last X hours
    time_threshold = datetime.now(timezone.utc) - timedelta(hours=hours_back)

    # Subquery to get the most recent entry per dup_entry_id, with a tie-breaker on entry_id
    latest_entries_subquery = (
        db.query(
            Entry.dup_entry_id,
            func.max(Entry.published).label("latest_published"),
            func.min(Entry.entry_id).label("tie_breaker_entry_id")  # Use the smallest entry_id as a tie-breaker
        )
        .filter(
            Entry.llm_positive >= min_positive_score,
            Entry.published >= time_threshold,
            Entry.iptc_newscode != None,  # Exclude entries without a category
            Entry.dup_entry_id != None   # Exclude entries without a duplicate group
        )
        .group_by(Entry.dup_entry_id)
        .subquery()
    )

    # Subquery to get the top N entries per category with distinct dup_entry_ids
    subquery = (
        db.query(
            Entry.entry_id,
            Entry.iptc_newscode,
            Entry.source,
            Entry.published,
            Entry.llm_positive,
            Entry.dup_entry_id,
            Entry.title,
            Entry.link,
            Entry.description,
            Entry.description_auto_generated,
            # Don't select preview_img, just check if it exists
            case((Entry.preview_img != None, True), else_=False).label("has_image"),
            func.row_number().over(
                partition_by=Entry.iptc_newscode,
                order_by=(Entry.published.desc(), Entry.entry_id.asc())  # Tie-breaker on entry_id
            ).label("row_number")
        )
        .join(
            latest_entries_subquery,
            and_(
                Entry.dup_entry_id == latest_entries_subquery.c.dup_entry_id,
                Entry.published == latest_entries_subquery.c.latest_published,
                Entry.entry_id == latest_entries_subquery.c.tie_breaker_entry_id  # Ensure tie-breaker is applied
            )
        )
        .filter(
            Entry.llm_positive >= min_positive_score,
            Entry.published >= time_threshold,
            Entry.iptc_newscode != None,  # Exclude entries without a category
            Entry.dup_entry_id != None   # Exclude entries without a duplicate group
        )
        .subquery()
    )

    # Main query: Join the subquery with Category and Source tables
    query = (
        db.query(
            Category.iptc_newscode,
            Category.category.label("category_name"),
            subquery.c.entry_id,
            subquery.c.source,
            subquery.c.published,
            subquery.c.llm_positive,
            subquery.c.dup_entry_id,
            subquery.c.title,
            subquery.c.link,
            subquery.c.description,
            subquery.c.description_auto_generated,
            subquery.c.has_image,
            Source.name.label("source_name")
        )
        .join(subquery, subquery.c.iptc_newscode == Category.iptc_newscode)
        .join(Source, subquery.c.source == Source.source, isouter=True)
        .filter(subquery.c.row_number <= limit_per_category)  # Limit to top N entries per category
        .order_by(Category.iptc_newscode, subquery.c.published.desc())
    )

    # Execute the query and fetch results
    results = query.all()

    # Precompute 'has_similar' flags for all entries
    dup_entry_ids = [result.dup_entry_id for result in results]
    similar_counts = (
        db.query(
            Entry.dup_entry_id,
            func.count(func.distinct(Entry.source)).label("source_count")  # Count distinct sources
        )
        .filter(Entry.dup_entry_id.in_(dup_entry_ids))  # Filter by relevant dup_entry_ids
        .group_by(Entry.dup_entry_id)  # Group by dup_entry_id
        .all()
    )

    # Update the similar_map to consider duplicates only if there are multiple sources
    similar_map = {dup_id: source_count > 1 for dup_id, source_count in similar_counts}

    # Group results by category and enrich with 'has_similar' flags
    grouped_entries = {}
    for result in results:
        category_code = result.iptc_newscode
        if category_code not in grouped_entries:
            grouped_entries[category_code] = {
                "name": result.category_name,
                "entries": []
            }

        grouped_entries[category_code]["entries"].append({
            **{k: v for k, v in result._asdict().items() if not k.startswith('_')},
            "has_similar": similar_map.get(result.dup_entry_id, False)
            # has_image is already included from the query
        })

    return grouped_entries


def get_similar_news(db: Session, entry_id: str) -> List[Dict]:
    """
    Fetches similar news to a specific news entry.
    
    Args:
        db: Database session
        entry_id: ID of the news entry to find similar news for
        
    Returns:
        List of dictionaries representing similar news entries
    """
    # Fetch the dup_entry_id of the given entry
    entry = db.query(
            Entry.entry_id,
            Entry.iptc_newscode,
            Entry.source,
            Entry.published,
            Entry.llm_positive,
            Entry.dup_entry_id,
            Entry.title,
            Entry.link,
            Entry.description,
            Entry.description_auto_generated,
            case((Entry.preview_img != None, True), else_=False).label("has_image"),
        ).filter(Entry.entry_id == entry_id).first()
    if not entry or not entry.dup_entry_id:
        return []

    # Subquery to get the most recent entry (with a tie-breaker on entry_id)
    # per source for the given dup_entry_id
    latest_entries_subquery = (
        db.query(
            Entry.dup_entry_id,
            func.max(Entry.published).label("latest_published"),
            func.min(Entry.entry_id).label("tie_breaker_entry_id")
        )
        .filter(Entry.dup_entry_id == entry.dup_entry_id)
        .group_by(
            Entry.dup_entry_id, 
            Entry.source
        )
        .subquery()
    )

    # Main query to fetch similar news
    query =(
        db.query(
            Entry.entry_id,
            Entry.iptc_newscode,
            Entry.source,
            Entry.published,
            Entry.llm_positive,
            Entry.dup_entry_id,
            Entry.title,
            Entry.link,
            Entry.description,
            Entry.description_auto_generated,
            case((Entry.preview_img != None, True), else_=False).label("has_image"),
            Category.category.label("category_name"),
            Source.name.label("source_name"),
        )
        .join(
            latest_entries_subquery,
            and_(
                Entry.dup_entry_id == latest_entries_subquery.c.dup_entry_id,
                Entry.entry_id == latest_entries_subquery.c.tie_breaker_entry_id
            )
        )
        .join(Category, Entry.iptc_newscode == Category.iptc_newscode, isouter=True)
        .join(Source, Entry.source == Source.source, isouter=True)
        .filter(
            Entry.entry_id != entry_id,  # Exclude the original entry
            Entry.source != entry.source  # Exclude entries from the same source
        )
    )   

    # Fetch results
    similar_entries = query.all()

    # Convert results to dictionaries and encode preview images
    original_dict = {
        **{k: v for k, v in entry._asdict().items() if not k.startswith('_')},
    }

    # Convert similar entries to dictionaries and encode preview images
    enriched_entries = [
        {
            **{k: v for k, v in entry._asdict().items() if not k.startswith('_')},
        }
        for entry in similar_entries
    ]

    return original_dict, enriched_entries


def get_sources(db: Session) -> List[Source]:
    """
    Ruft alle verfügbaren Nachrichtenquellen ab.
    
    Args:
        db: Datenbankverbindung
        
    Returns:
        Liste aller Source-Objekte
    """
    return db.query(Source).all()


def get_source_by_id(db: Session, source_id: str) -> Optional[Source]:
    """
    Ruft eine bestimmte Nachrichtenquelle anhand ihrer ID ab.
    
    Args:
        db: Datenbankverbindung
        source_id: ID der Quelle
        
    Returns:
        Source-Objekt oder None, wenn nicht gefunden
    """
    return db.query(Source).filter(Source.source == source_id).first()


def get_categories(db: Session) -> List[Category]:
    """
    Ruft alle verfügbaren Kategorien ab.
    
    Args:
        db: Datenbankverbindung
        
    Returns:
        Liste aller Category-Objekte
    """
    return db.query(Category).all()


def get_category_by_id(db: Session, category_id: str) -> Optional[Category]:
    """
    Ruft eine bestimmte Kategorie anhand ihrer ID ab.
    
    Args:
        db: Datenbankverbindung
        category_id: ID der Kategorie (iptc_newscode)
        
    Returns:
        Category-Objekt oder None, wenn nicht gefunden
    """
    return db.query(Category).filter(Category.iptc_newscode == category_id).first()


def get_entry_by_id(db: Session, entry_id: str) -> Optional[Entry]:
    """
    Retrieves a news entry by its ID.
    
    Args:
        db: Database session
        entry_id: ID of the entry
        
    Returns:
        Entry object or None if not found
    """
    return db.query(Entry).filter(Entry.entry_id == entry_id).first()
