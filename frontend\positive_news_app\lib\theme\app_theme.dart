import 'package:flutter/material.dart';

class AppTheme {
  // Colors
  static const Color primaryColor = Color(0xFFFFC41E);    // Yellow primary
  static const Color secondaryColor = Color(0xFFFF9800);   // Orange secondary
  static const Color accentColor = Color(0xFF2196F3);     // Blue accent
  static const Color errorColor = Color(0xFFE57373);      // Red error
  static const Color backgroundColor = Color(0xFFFFFBE6);  // Light yellow background
  static const Color cardColor = Colors.white;
  static const Color textColor = Color(0xFF212121);
  static const Color secondaryTextColor = Color(0xFF757575);

  // Dark theme colors
  static const Color darkPrimaryColor = Color(0xFF998855);    // Darker, greyish yellow
  static const Color darkSecondaryColor = Color(0xFFF57C00);  // Darker orange
  static const Color darkAccentColor = Color(0xFF1976D2);     // Darker blue
  static const Color darkErrorColor = Color(0xFFEF5350);
  static const Color darkBackgroundColor = Color(0xFF121212);
  static const Color darkCardColor = Color(0xFF1E1E1E);
  static const Color darkTextColor = Color(0xFFEEEEEE);
  static const Color darkSecondaryTextColor = Color(0xFFBDBDBD);

  // Text Themes
  static const TextTheme lightTextTheme = TextTheme(
    displayLarge: TextStyle(fontSize: 28.0, fontWeight: FontWeight.bold, color: textColor),
    displayMedium: TextStyle(fontSize: 24.0, fontWeight: FontWeight.bold, color: textColor),
    displaySmall: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold, color: textColor),
    headlineMedium: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold, color: textColor),
    titleLarge: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold, color: textColor),
    bodyLarge: TextStyle(fontSize: 16.0, color: textColor),
    bodyMedium: TextStyle(fontSize: 14.0, color: textColor),
    bodySmall: TextStyle(fontSize: 12.0, color: secondaryTextColor),
  );

  static const TextTheme darkTextTheme = TextTheme(
    displayLarge: TextStyle(fontSize: 28.0, fontWeight: FontWeight.bold, color: darkTextColor),
    displayMedium: TextStyle(fontSize: 24.0, fontWeight: FontWeight.bold, color: darkTextColor),
    displaySmall: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold, color: darkTextColor),
    headlineMedium: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold, color: darkTextColor),
    titleLarge: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold, color: darkTextColor),
    bodyLarge: TextStyle(fontSize: 16.0, color: darkTextColor),
    bodyMedium: TextStyle(fontSize: 14.0, color: darkTextColor),
    bodySmall: TextStyle(fontSize: 12.0, color: darkSecondaryTextColor),
  );

  // Light Theme
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.light(
      primary: primaryColor,
      secondary: secondaryColor,
      error: errorColor,
      surface: cardColor,
    ),
    scaffoldBackgroundColor: backgroundColor,
    cardTheme: const CardTheme(
      color: cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    textTheme: lightTextTheme,
    buttonTheme: ButtonThemeData(
      buttonColor: primaryColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
      ),
    ),
    iconTheme: const IconThemeData(
      color: primaryColor,
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: Colors.white,
      selectedItemColor: primaryColor,
      unselectedItemColor: secondaryTextColor,
    ),
  );

  // Dark Theme
  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.dark(
      primary: darkPrimaryColor,
      secondary: darkSecondaryColor,
      error: darkErrorColor,
      surface: darkCardColor,
    ),
    scaffoldBackgroundColor: darkBackgroundColor,
    cardTheme: const CardTheme(
      color: darkCardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: darkPrimaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    textTheme: darkTextTheme,
    buttonTheme: ButtonThemeData(
      buttonColor: darkPrimaryColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: darkPrimaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: darkPrimaryColor,
      ),
    ),
    iconTheme: const IconThemeData(
      color: darkPrimaryColor,
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: darkCardColor,
      selectedItemColor: darkPrimaryColor,
      unselectedItemColor: darkSecondaryTextColor,
    ),
  );
}
