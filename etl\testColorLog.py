import logging
import colorlog

log_colors = {
    'DEBUG': 'white',
    'INFO': 'cyan',
    'WARNING': 'yellow',
    'ERROR': 'red',
    'CRITICAL': 'red,bg_white',
}

class CustomColoredFormatter(colorlog.ColoredFormatter):
    def format(self, record):
        if 'PURPLE' in record.msg:
            self.secondary_log_colors = {
                'message': {
                    'DEBUG': 'purple',
                    'INFO': 'purple',
                    'WARNING': 'purple',
                    'ERROR': 'purple',
                    'CRITICAL': 'purple',
                }
            }
        else:
            self.secondary_log_colors = {
                'message': {}}
        return super().format(record)

formatter = CustomColoredFormatter(
    "%(log_color)s%(asctime)s - %(levelname)s - %(log_color)s%(message_log_color)s%(message)s",
    log_colors=log_colors
)

handler = colorlog.StreamHandler()
handler.setFormatter(formatter)
logger = logging.getLogger()
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)

logger.debug("This is a STANDARD debug message")
logger.debug("This is a PURPLE debug message")
logger.info("This is an STANDARD info message")
logger.info("This is an PURPLE info message")
logger.warning("This is a STANDARD warning message")
logger.warning("This is a PURPLE warning message")
logger.error("This is an STANDARD error message")
logger.error("This is an PURPLE error message")
logger.critical("This is a STANDARD critical message")
logger.critical("This is a PURPLE critical message")