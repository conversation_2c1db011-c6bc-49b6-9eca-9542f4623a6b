import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:positive_news_app/models/news_model.dart';
import 'package:positive_news_app/providers/settings_provider.dart';
import 'package:positive_news_app/services/api_service.dart';
import 'package:positive_news_app/widgets/responsive_news_grid.dart';
import 'package:positive_news_app/screens/settings/settings_screen.dart'; // Import the SettingsScreen
import 'package:provider/provider.dart';

class CategoryViewScreen extends StatefulWidget {
  final String categoryCode;
  final String categoryName;

  const CategoryViewScreen({
    super.key,
    required this.categoryCode,
    required this.categoryName,
  });

  @override
  State<CategoryViewScreen> createState() => _CategoryViewScreenState();
}

class _CategoryViewScreenState extends State<CategoryViewScreen> {
  final ScrollController _scrollController = ScrollController();
  final List<EntryWithSource> _newsList = []; // Updated to use EntryWithSource
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 0;
  final int _pageSize = 10;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    // Wenn wir die drittletzte Nachricht erreichen, laden wir mehr Nachrichten
    if (_scrollController.position.pixels >
        _scrollController.position.maxScrollExtent - 300) {
      if (!_isLoading && _hasMore) {
        _loadMoreData();
      }
    }
  }

  Future<void> _loadInitialData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      final response = await apiService.getNewsByCategory(
        categoryCode: widget.categoryCode,
        skip: 0,
        limit: _pageSize,
        lookbackHours: Provider.of<SettingsProvider>(context, listen: false).lookbackHours,
      );

      setState(() {
        _newsList.clear();
        _newsList.addAll(response.items);
        _hasMore = response.hasMore;
        _currentPage = 1;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Fehler beim Laden der Nachrichten: $e');
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      final response = await apiService.getNewsByCategory(
        categoryCode: widget.categoryCode,
        skip: _currentPage * _pageSize,
        limit: _pageSize,
        lookbackHours: Provider.of<SettingsProvider>(context, listen: false).lookbackHours,
      );

      setState(() {
        _newsList.addAll(response.items);
        _hasMore = response.hasMore;
        _currentPage++;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Fehler beim Laden weiterer Nachrichten: $e');
    }
  }

  Future<void> _refreshData() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      final apiService = Provider.of<ApiService>(context, listen: false);
      final response = await apiService.getNewsByCategory(
        categoryCode: widget.categoryCode,
        skip: 0,
        limit: _pageSize,
        lookbackHours: Provider.of<SettingsProvider>(context, listen: false).lookbackHours,
      );

      setState(() {
        _newsList.clear();
        _newsList.addAll(response.items);
        _hasMore = response.hasMore;
        _currentPage = 1;
        _isRefreshing = false;
      });
    } catch (e) {
      setState(() {
        _isRefreshing = false;
      });
      _showErrorSnackBar('Fehler beim Aktualisieren der Nachrichten: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            SvgPicture.asset(
              'assets/icons/logo.svg',
              height: 28,
              width: 28,
              alignment: Alignment.center,
            ),
            const SizedBox(width: 8),
            Text(widget.categoryName),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () async {
              final settingsChanged = await Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
              
              if (settingsChanged == true) {
                _loadInitialData(); // Reload data when returning from settings
              }
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: _newsList.isEmpty && _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _newsList.isEmpty
                ? const Center(child: Text('Keine Nachrichten gefunden'))
                : ResponsiveNewsGrid(
                    newsList: _newsList,
                    showFavoriteButton: true,
                    scrollController: _scrollController,
                  ),
      ),
    );
  }
}

