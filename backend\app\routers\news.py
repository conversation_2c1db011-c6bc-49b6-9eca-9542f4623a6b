from fastapi import APIRouter, Depends, HTTPException, Query, logger, Response
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta

from backend.app.core.database import get_db
from backend.app.core import crud

from backend.app.models import schemas

router = APIRouter()


@router.get("/news/", response_model=schemas.PaginatedNewsResponse)
async def read_news(
    skip: int = 0,
    limit: int = 10,
    min_positive: float = 0.9,
    hours: int = 24,
    category: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Ruft positive Nachrichten ab, basierend auf den Filterkriterien.
    Unterstützt Pagination und Filterung nach Kategorie.
    """
    news, total = crud.get_positive_news(
        db=db,
        skip=skip,
        limit=limit,
        min_positive_score=min_positive,
        hours_back=hours,
        category=category,
        search=search
    )
    
    # Quellennamen und Kategorienamen hinzufügen
    result = []
    for entry in news:
        # Check if entry is a dict or SQLAlchemy model
        if isinstance(entry, dict):
            source_id = entry.get('source')
            iptc_code = entry.get('iptc_newscode')
            entry_id = entry.get('entry_id')
            entry_dict = entry
        else:
            source_id = entry.source
            iptc_code = entry.iptc_newscode
            entry_id = entry.entry_id
            entry_dict = {k: v for k, v in entry.__dict__.items() if not k.startswith('_')}
        
        entry_with_source = schemas.EntryWithSource(
            **entry_dict
        )
        result.append(entry_with_source)
    
    # Prüfen, ob es weitere Ergebnisse gibt
    has_more = (skip + limit) < total
    
    return schemas.PaginatedNewsResponse(
        items=result,
        total=total,
        has_more=has_more
    )


@router.get("/news/categories/", response_model=schemas.NewsResponse)
async def read_news_by_categories(
    min_positive: float = 0.9,
    hours: int = 24,
    limit_per_category: int = 3,
    db: Session = Depends(get_db)
):
    """
    Ruft positive Nachrichten gruppiert nach Kategorien ab.
    """
    news_by_category = crud.get_news_by_category(
        db=db,
        min_positive_score=min_positive,
        hours_back=hours,
        limit_per_category=limit_per_category
    )

    # Format the response directly from the enriched data
    categories_list = [ 
        schemas.CategoryNews(
            category=category_data["name"],
            category_code=category_code,
            news=[
                schemas.EntryWithSource(**entry) for entry in category_data["entries"]
            ]
        )
        for category_code, category_data in news_by_category.items()
    ]

    return schemas.NewsResponse(categories=categories_list)


@router.get("/news/category/{category_code}", response_model=schemas.PaginatedNewsResponse)
async def read_news_by_category(
    category_code: str,
    skip: int = 0,
    limit: int = 10,
    min_positive: float = 0.9,
    hours: int = 24,
    db: Session = Depends(get_db)
):
    """
    Fetches positive news for a specific category.
    Supports pagination for the category view.
    """
    # Fetch enriched news and total count from the database
    news, total = crud.get_positive_news(
        db=db,
        skip=skip,
        limit=limit,
        min_positive_score=min_positive,
        hours_back=hours,
        category=category_code
    )

    # Check if there are more results
    has_more = (skip + limit) < total

    return schemas.PaginatedNewsResponse(
        items=[schemas.EntryWithSource(**entry) for entry in news],
        total=total,
        has_more=has_more
    )


@router.get("/news/{entry_id}/similar", response_model=schemas.SimilarNewsResponse)
async def read_similar_news(
    entry_id: str,
    db: Session = Depends(get_db)
):
    """
    Fetches similar news to a specific news entry, enriched with 'has_similar' flags.
    """
    # Fetch similar news using the modified `get_similar_news`
    original, similar_entries = crud.get_similar_news(db, entry_id)

    return schemas.SimilarNewsResponse(
        original=original,
        similar_sources=similar_entries)


@router.get("/sources/", response_model=List[schemas.Source])
async def read_sources(db: Session = Depends(get_db)):
    """
    Ruft alle verfügbaren Nachrichtenquellen ab.
    """
    return crud.get_sources(db)


@router.get("/categories/", response_model=List[schemas.Category])
async def read_categories(db: Session = Depends(get_db)):
    """
    Ruft alle verfügbaren Kategorien ab.
    """
    return crud.get_categories(db)

@router.get("/news/home", response_model=schemas.HomeScreenResponse)
async def read_home_screen_data(
    min_positive: float = 0.7,
    hours: int = 24,
    limit_per_category: int = 3,
    db: Session = Depends(get_db)
):
    """
    Fetches all data required for the home screen in a single response.
    """
    # Fetch enriched news data grouped by category
    news_by_category = crud.get_news_by_category(
        db=db,
        min_positive_score=min_positive,
        hours_back=hours,
        limit_per_category=limit_per_category
    )

    # Format the response directly from the enriched data
    categories_list = [
        schemas.CategoryNews(
            category=category_data["name"],
            category_code=category_code,
            news=[
                schemas.EntryWithSource(**entry) for entry in category_data["entries"]
            ]
        )
        for category_code, category_data in news_by_category.items()
    ]

    return schemas.HomeScreenResponse(categories=categories_list)


@router.get("/news/{entry_id}/image", response_class=Response)
async def get_news_image(
    entry_id: str,
    db: Session = Depends(get_db)
):
    """
    Retrieves the preview image for a specific news entry.
    Returns the image as binary data with appropriate content type.
    """
    entry = crud.get_entry_by_id(db, entry_id)
    if not entry or not entry.preview_img:
        raise HTTPException(status_code=404, detail="Image not found")
    
    return Response(content=entry.preview_img, media_type="image/jpeg")
