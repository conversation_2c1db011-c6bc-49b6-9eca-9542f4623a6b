from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from backend.app.core.config import settings

# Create SQLAlchemy engine using the DATABASE_URL from settings
engine = create_engine(settings.DATABASE_URL, echo=True)
        
# Create sessionmaker
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()


def get_db():
    """
    Dependency for FastAPI to provide a database connection.
    Ensures the connection is closed after use.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
