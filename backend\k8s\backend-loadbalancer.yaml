apiVersion: v1
kind: Service
metadata:
  name: breaking-bright-api-lb
  namespace: default
  annotations:
    service.beta.kubernetes.io/oci-load-balancer-shape: "flexible"
    service.beta.kubernetes.io/oci-load-balancer-shape-flex-min: "10"
    service.beta.kubernetes.io/oci-load-balancer-shape-flex-max: "10"
    service.beta.kubernetes.io/oci-load-balancer-subnet1: "ocid1.subnet.oc1.eu-frankfurt-1.aaaaaaaaybcnksw4cvtgwue6m4lwcan73wd5j4egnbm4znf4wg6nycmg2j7a"
spec:
  type: LoadBalancer
  selector:
    app: breaking-bright-backend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
