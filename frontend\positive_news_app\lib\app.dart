import 'package:flutter/material.dart';
import 'package:positive_news_app/providers/settings_provider.dart';
import 'package:positive_news_app/services/api_service.dart';
import 'package:provider/provider.dart';
import 'package:positive_news_app/services/connection_status_provider.dart';
import 'package:positive_news_app/screens/home/<USER>';
import 'package:positive_news_app/screens/onboarding/onboarding_screen.dart';
import 'package:positive_news_app/screens/test/api_integration_test.dart';
import 'package:positive_news_app/theme/app_theme.dart';
import 'package:positive_news_app/utils/performance_optimizer.dart';
import 'package:positive_news_app/utils/platform_optimizer.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PositiveNewsApp extends StatefulWidget {
  const PositiveNewsApp({super.key});

  @override
  State<PositiveNewsApp> createState() => _PositiveNewsAppState();
}

class _PositiveNewsAppState extends State<PositiveNewsApp> {
  bool _showOnboarding = true;
  bool _isLoading = true;
  ThemeMode _themeMode = ThemeMode.system;

  @override
  void initState() {
    super.initState();
    _checkFirstRun();
  }

  Future<void> _checkFirstRun() async {
    final prefs = await SharedPreferences.getInstance();
    final hasCompletedOnboarding = prefs.getBool('hasCompletedOnboarding') ?? false;
    final savedThemeMode = prefs.getString('themeMode');
    
    setState(() {
      _showOnboarding = !hasCompletedOnboarding;
      if (savedThemeMode == 'dark') {
        _themeMode = ThemeMode.dark;
      } else if (savedThemeMode == 'light') {
        _themeMode = ThemeMode.light;
      }
      _isLoading = false;
    });
  }

  void setThemeMode(ThemeMode mode) async {
    setState(() {
      _themeMode = mode;
    });
    
    final prefs = await SharedPreferences.getInstance();
    if (mode == ThemeMode.dark) {
      await prefs.setString('themeMode', 'dark');
    } else if (mode == ThemeMode.light) {
      await prefs.setString('themeMode', 'light');
    } else {
      await prefs.setString('themeMode', 'system');
    }
  }

  void completeOnboarding() async {
    setState(() {
      _showOnboarding = false;
    });
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('hasCompletedOnboarding', true);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    PerformanceOptimizer.optimizeForPlatform(context);
    PerformanceOptimizer.optimizeImageCache();
    PlatformOptimizer.optimizeForCurrentPlatform(context);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const MaterialApp(
        home: Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
        Provider<ApiService>(create: (_) => ApiService()),
        ChangeNotifierProvider(
          create: (context) => ConnectionStatusProvider(
            Provider.of<ApiService>(context, listen: false),
          ),
        ),
      ],
      child: MaterialApp(
        title: 'Breaking Bright',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: _themeMode,
        debugShowCheckedModeBanner: false,
        home: _showOnboarding 
            ? OnboardingScreen(onComplete: completeOnboarding)
            : _buildHomeWithConnectionCheck(),
      ),
    );
  }

  Widget _buildHomeWithConnectionCheck() {
    return Consumer<ConnectionStatusProvider>(
      builder: (context, connectionProvider, child) {
        if (connectionProvider.isLoading) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Verbindung zum Server wird hergestellt...'),
                ],
              ),
            ),
          );
        }

        if (!connectionProvider.isConnected) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.cloud_off,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Keine Verbindung zum Server',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    connectionProvider.errorMessage.isNotEmpty
                        ? connectionProvider.errorMessage
                        : 'Bitte überprüfen Sie Ihre Internetverbindung',
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => connectionProvider.checkConnection(),
                    child: const Text('Erneut versuchen'),
                  ),
                ],
              ),
            ),
          );
        }

        // For testing purposes, we can switch between HomeScreen and ApiIntegrationTest
        const bool showTestScreen = false;
        
        return showTestScreen
            // ignore: dead_code
            ? const ApiIntegrationTest()
            : HomeScreen(
                setThemeMode: setThemeMode,
                currentThemeMode: _themeMode,
              );
      },
    );
  }
}
