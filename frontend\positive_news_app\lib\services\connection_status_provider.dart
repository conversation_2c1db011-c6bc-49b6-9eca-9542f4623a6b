import 'package:flutter/material.dart';
import 'package:positive_news_app/services/api_service.dart';

class ConnectionStatusProvider extends ChangeNotifier {
  final ApiService _apiService;
  bool _isConnected = false;
  bool _isLoading = true;
  String _errorMessage = '';

  ConnectionStatusProvider(this._apiService) {
    checkConnection();
  }

  bool get isConnected => _isConnected;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  Future<void> checkConnection() async {
    _isLoading = true;
    _errorMessage = '';
    notifyListeners();

    try {
      _isConnected = await _apiService.testConnection();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isConnected = false;
      _isLoading = false;
      _errorMessage = 'Verbindungsfehler: $e';
      notifyListeners();
    }
  }
}
