import 'package:flutter/material.dart';
import 'package:positive_news_app/utils/platform_optimizer.dart';

class ResponsiveLayoutBuilder extends StatelessWidget {
  final Widget Function(BuildContext, BoxConstraints) builder;

  const ResponsiveLayoutBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Optimiere Layout basierend auf Bildschirmgröße und Plattform
        return builder(context, constraints);
      },
    );
  }

  // Hilfsmethode zur Bestimmung des Layouts basierend auf der Bildschirmbreite
  static ScreenSize getScreenSize(BoxConstraints constraints) {
    if (constraints.maxWidth < 600) {
      return ScreenSize.small;
    } else if (constraints.maxWidth < 900) {
      return ScreenSize.medium;
    } else {
      return ScreenSize.large;
    }
  }

  // Hilfsmethode zur Anpassung der Spaltenanzahl basierend auf der Bildschirmgröße
  static int getColumnCount(BoxConstraints constraints) {
    final screenSize = getScreenSize(constraints);
    
    if (PlatformOptimizer.isWeb()) {
      // Web-spezifische Anpassungen
      switch (screenSize) {
        case ScreenSize.small:
          return 1;
        case ScreenSize.medium:
          return 2;
        case ScreenSize.large:
          return 3;
      }
    } else {
      // Mobile Anpassungen
      switch (screenSize) {
        case ScreenSize.small:
          return 1;
        case ScreenSize.medium:
          return 2;
        case ScreenSize.large:
          return 2;
      }
    }
  }

  // Hilfsmethode zur Anpassung der Schriftgröße basierend auf der Bildschirmgröße
  static double getFontSizeMultiplier(BoxConstraints constraints) {
    final screenSize = getScreenSize(constraints);
    
    switch (screenSize) {
      case ScreenSize.small:
        return 1.0;
      case ScreenSize.medium:
        return 1.1;
      case ScreenSize.large:
        return 1.2;
    }
  }

  // Hilfsmethode zur Anpassung des Paddings basierend auf der Bildschirmgröße
  static EdgeInsets getPadding(BoxConstraints constraints) {
    final screenSize = getScreenSize(constraints);
    
    switch (screenSize) {
      case ScreenSize.small:
        return const EdgeInsets.all(8.0);
      case ScreenSize.medium:
        return const EdgeInsets.all(16.0);
      case ScreenSize.large:
        return const EdgeInsets.all(24.0);
    }
  }
}

// Enum für Bildschirmgrößen
enum ScreenSize {
  small,
  medium,
  large,
}
