import 'package:flutter/material.dart';
import 'package:positive_news_app/screens/home/<USER>';
import 'package:provider/provider.dart';
import 'package:positive_news_app/services/api_service.dart';
import 'package:positive_news_app/services/connection_status_provider.dart';

class ApiIntegrationTest extends StatefulWidget {
  const ApiIntegrationTest({super.key});

  @override
  State<ApiIntegrationTest> createState() => _ApiIntegrationTestState();
}

class _ApiIntegrationTestState extends State<ApiIntegrationTest> {
  bool _isLoading = false;
  String _testResult = '';
  List<String> _testLog = [];
  bool _allTestsPassed = false;

  @override
  Widget build(BuildContext context) {
    final apiService = Provider.of<ApiService>(context, listen: false);
    final connectionProvider = Provider.of<ConnectionStatusProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('API Integration Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => HomeScreen(
                    setThemeMode: (ThemeMode mode) {},
                    currentThemeMode: ThemeMode.system,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Verbindungsstatus:',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          connectionProvider.isConnected
                              ? Icons.check_circle
                              : Icons.error,
                          color: connectionProvider.isConnected
                              ? Colors.green
                              : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          connectionProvider.isConnected
                              ? 'Verbunden'
                              : 'Nicht verbunden',
                          style: TextStyle(
                            color: connectionProvider.isConnected
                                ? Colors.green
                                : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    if (connectionProvider.errorMessage.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Fehlermeldung: ${connectionProvider.errorMessage}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: connectionProvider.isLoading
                          ? null
                          : () => connectionProvider.checkConnection(),
                      child: Text(
                        connectionProvider.isLoading
                            ? 'Prüfe Verbindung...'
                            : 'Verbindung prüfen',
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'API-Tests:',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : () async {
                      setState(() {
                        _isLoading = true;
                        _testResult = 'Tests werden ausgeführt...';
                        _testLog = [];
                        _allTestsPassed = false;
                      });

                      try {
                        // Test 1: Verbindung prüfen
                        _addToLog('Test 1: Verbindung zum Backend prüfen');
                        final isConnected = await apiService.testConnection();
                        if (!isConnected) {
                          throw Exception('Keine Verbindung zum Backend');
                        }
                        _addToLog('✓ Verbindung erfolgreich hergestellt');

                        // Test 2: Nachrichten nach Kategorien abrufen
                        _addToLog('Test 2: Nachrichten nach Kategorien abrufen');
                        final newsResponse = await apiService.getNewsByCategories();
                        _addToLog('✓ ${newsResponse.categories.length} Kategorien erhalten');

                        // Test 3: Einzelne Nachrichten abrufen
                        _addToLog('Test 3: Einzelne Nachrichten abrufen');
                        final newsList = await apiService.getNews();
                        _addToLog('✓ ${newsList.items.length} Nachrichten erhalten');

                        // Test 4: Wenn Nachrichten vorhanden, ähnliche Nachrichten abrufen
                        if (newsList.hasMore) {
                          _addToLog('Test 4: Ähnliche Nachrichten abrufen');
                          final similarNews = await apiService.getSimilarNews(newsList.items[0].entryId);
                          _addToLog('✓ ${similarNews.similarSources.length} ähnliche Nachrichten für ${newsList.items[0].entryId} erhalten');
                        } else {
                          _addToLog('Test 4: Übersprungen (keine Nachrichten verfügbar)');
                        }

                        // Test 5: Suche testen
                        _addToLog('Test 5: Suche testen');
                        final searchResults = await apiService.searchNews('test');
                        _addToLog('✓ Suche durchgeführt, ${searchResults.items.length} Ergebnisse erhalten');

                        setState(() {
                          _testResult = 'Alle Tests erfolgreich abgeschlossen!';
                          _allTestsPassed = true;
                        });
                      } catch (e) {
                        setState(() {
                          _testResult = 'Fehler bei den Tests: $e';
                        });
                      } finally {
                        setState(() {
                          _isLoading = false;
                        });
                      }
                    },
              child: Text(_isLoading ? 'Tests laufen...' : 'Tests ausführen'),
            ),
            const SizedBox(height: 16),
            Text(
              'Ergebnis:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _testResult,
              style: TextStyle(
                color: _allTestsPassed ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Test-Log:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black87,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: _testLog.length,
                  itemBuilder: (context, index) {
                    return Text(
                      _testLog[index],
                      style: const TextStyle(
                        color: Colors.white,
                        fontFamily: 'monospace',
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addToLog(String message) {
    setState(() {
      _testLog.add('${DateTime.now().toString().substring(11, 19)} - $message');
    });
  }
}
