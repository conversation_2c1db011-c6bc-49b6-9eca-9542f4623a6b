import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:positive_news_app/models/news_model.dart';
import 'package:positive_news_app/screens/detail/detail_screen.dart';
import 'package:positive_news_app/screens/web_view/web_view_screen.dart';
import 'package:positive_news_app/services/api_service.dart';
import 'package:provider/provider.dart';
import 'package:positive_news_app/services/favorites_service.dart';
import 'package:url_launcher/url_launcher.dart';

class NewsCard extends StatelessWidget {
  // Make constants static and public (remove underscore)
  static const double bottomBarHeight = 48.0;
  static const double iconSize = 20.0;
  static const double imageHeight = 200.0;

  final EntryWithSource news;
  final bool showFavoriteButton;
  final bool showSimilarMessage;
  final double? width;

  const NewsCard({
    super.key,
    required this.news,
    this.showFavoriteButton = true,
    this.showSimilarMessage = true,
    this.width,
  });

  Future<void> _openNewsLink(BuildContext context, String url, [bool newTab = false]) async {
    final Uri uri = Uri.parse(url);
    
    if (kIsWeb && newTab) {
      try {
        await launchUrl(
          uri,
          mode: LaunchMode.platformDefault,  // For web, use platform default (new tab)
        );
        return;
      } catch (e) {
        debugPrint('Failed to open URL in new tab: $e');
      }
    }
    
    // Check if context is still mounted before proceeding
    if (!context.mounted) return;
    
    // Try WebView approach first - no async gap before using context
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WebViewScreen(
            url: url,
            title: news.title,
          ),
        ),
      );
      return; // Exit if successful
    } catch (e) {
      debugPrint('Custom WebView failed: $e');
      // If WebView fails, continue to next approach
    }
    
    // For the remaining approaches, we'll use async operations
    // so we need to handle context carefully
    _tryAlternativeApproaches(context, uri, url);
  }

  // Separate method to handle alternative URL opening approaches
  Future<void> _tryAlternativeApproaches(BuildContext context, Uri uri, String url) async {
    try {
      // Try in-app browser
      final bool launched = await launchUrl(
        uri,
        mode: LaunchMode.inAppWebView,
        webViewConfiguration: const WebViewConfiguration(
          enableJavaScript: true,
          enableDomStorage: true,
        ),
      );
      
      if (!launched) {
        throw Exception('Could not launch URL in in-app browser');
      }
    } catch (e) {
      // Try external browser as final fallback
      try {
        final bool launched = await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        
        if (!launched) {
          throw Exception('Could not launch URL in external browser');
        }
      } catch (fallbackError) {
        // Check if context is still mounted before showing snackbar
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Konnte Link nicht öffnen: $url')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      child: IntrinsicHeight(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top content section
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image with fixed height
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4.0),
                    topRight: Radius.circular(4.0),
                  ),
                  child: CachedNetworkImage(
                    imageUrl: Provider.of<ApiService>(context, listen: false)
                        .getImageUrl(news.entryId, hasImage: news.hasImage == true),
                    height: imageHeight,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      height: imageHeight,
                      color: Colors.grey[300],
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                    errorWidget: (context, url, error) => Container(
                      height: imageHeight,
                      color: Colors.grey[300],
                      child: const Icon(Icons.error),
                    ),
                  ),
                ),
                
                // Content area
                kIsWeb 
                  ? Listener(
                      onPointerDown: (PointerDownEvent event) {
                        // Check if Ctrl key is pressed for web platform
                        if (event.kind == PointerDeviceKind.mouse && 
                            (event.buttons & kSecondaryMouseButton != 0 || 
                             event.down && event.buttons == kPrimaryMouseButton && 
                             (HardwareKeyboard.instance.isControlPressed))) {
                          _openNewsLink(context, news.link, true);
                        } else {
                          _openNewsLink(context, news.link, false);
                        }
                      },
                      child: _buildContentArea(context),
                    )
                  : InkWell(
                      onTap: () => _openNewsLink(context, news.link),
                      child: _buildContentArea(context),
                    ),
              ],
            ),
            
            // Fixed height bottom bar
            SizedBox(
              height: bottomBarHeight,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Similar news button
                    if (news.hasSimilar && showSimilarMessage)
                      Expanded(
                        child: SizedBox(
                          height: bottomBarHeight,
                          child: TextButton.icon(
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(horizontal: 8.0),
                              minimumSize: Size.zero,
                              alignment: Alignment.centerLeft,
                              // Add visual feedback with splash and highlight colors
                              foregroundColor: Theme.of(context).colorScheme.primary,
                            ).copyWith(
                              overlayColor: WidgetStateProperty.resolveWith<Color?>(
                                (Set<WidgetState> states) {
                                  if (states.contains(WidgetState.pressed)) {
                                    return Theme.of(context).colorScheme.primary.withAlpha(31); // ~12% opacity
                                  }
                                  if (states.contains(WidgetState.hovered)) {
                                    return Theme.of(context).colorScheme.primary.withAlpha(20); // ~8% opacity
                                  }
                                  return null;
                                },
                              ),
                            ),
                            icon: const Icon(
                              Icons.compare_arrows,
                              size: iconSize,
                            ),
                            label: const Text(
                              'Ähnliche Nachrichten verfügbar',
                              style: TextStyle(fontSize: 12.0),
                              overflow: TextOverflow.ellipsis,
                            ),
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => DetailScreen(news: news),
                                ),
                              );
                            },
                          ),
                        ),
                      )
                    else
                      const Spacer(),
                    
                    // Favorite button
                    if (showFavoriteButton)
                      SizedBox(
                        width: bottomBarHeight,
                        height: bottomBarHeight,
                        child: Consumer<FavoritesService>(
                          builder: (context, favoritesService, child) {
                            final isFavorite = favoritesService.isFavorite(news);
                            return IconButton(
                              icon: Icon(
                                isFavorite ? Icons.favorite : Icons.favorite_border,
                                color: isFavorite ? Colors.red : null,
                                size: iconSize,
                              ),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                              onPressed: () {
                                if (isFavorite) {
                                  favoritesService.removeFavorite(news);
                                } else {
                                  favoritesService.addFavorite(news);
                                }
                              },
                            );
                          },
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildContentArea(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            news.title,
            style: Theme.of(context).textTheme.titleLarge,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                news.sourceName ?? news.source,
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(width: 8),
              Text(
                news.formattedDate,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
          if (news.description != null && news.description!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              news.formattedDescription!,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}






