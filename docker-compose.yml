version: '3'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: robertschulze/breaking-bright-backend:latest
    ports:
      - "8000:8000"
    env_file:
      - .env
    restart: always
    volumes:
      - ./backend/app:/app/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  etl:
    build:
      context: ./etl
      dockerfile: Dockerfile
    image: robertschulze/breaking-bright-etl:latest
    env_file:
      - .env
    restart: always
    volumes:
      - ./etl:/app/etl
      - ./etl/news_sources.yaml:/app/etl/news_sources.yaml
    depends_on:
      - backend

  web:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./frontend/positive_news_app/build/web:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - backend
    restart: always
