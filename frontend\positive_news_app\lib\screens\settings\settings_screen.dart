import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:positive_news_app/providers/settings_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Einstellungen'),
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context, true); // Always return true since settings might have changed
          },
        ),
      ),
      body: ListView(
        children: [
          _buildLookbackHoursSetting(context),
        ],
      ),
    );
  }

  Widget _buildLookbackHoursSetting(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Zeitraum für Nachrichten',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Zeige Nachrichten der letzten ${settings.lookbackHours} Stunden',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Slider(
                value: settings.lookbackHours.toDouble(),
                min: 24,
                max: 168, // 1 week
                divisions: 6,
                label: '${settings.lookbackHours}h',
                onChanged: (value) {
                  settings.setLookbackHours(value.round());
                },
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: const [
                  Text('24h'),
                  Text('168h'),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}







