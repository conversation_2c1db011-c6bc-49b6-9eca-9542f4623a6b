import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:positive_news_app/models/news_model.dart';
import 'package:positive_news_app/services/api_service.dart';
import 'package:positive_news_app/widgets/category_news_widget.dart';
import 'package:positive_news_app/widgets/search_bar_widget.dart';
import 'package:positive_news_app/widgets/responsive_news_grid.dart';
import 'package:provider/provider.dart';
import 'package:positive_news_app/services/favorites_service.dart';
import 'package:positive_news_app/screens/category/category_view_screen.dart';
import 'package:positive_news_app/screens/settings/settings_screen.dart';

class HomeScreen extends StatefulWidget {
  final Function(ThemeMode) setThemeMode;
  final ThemeMode currentThemeMode;

  const HomeScreen({
    super.key,
    required this.setThemeMode,
    required this.currentThemeMode,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ApiService _apiService = ApiService();
  NewsResponse? _newsData;
  bool _isLoading = false;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    debugPrint('HomeScreen initialized');
    _loadNews();
  }

  Future<void> _loadNews() async {
    if (_isLoading) return;
    
    debugPrint('Loading news data...');
    setState(() {
      _isLoading = true;
    });

    try {
      final newsResponse = await _apiService.getHomeScreenData(
        context: context,
        minPositive: 0.7,
        limitPerCategory: 3,
      );
      if (mounted) {
        setState(() {
          _newsData = newsResponse;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            SvgPicture.asset(
              'assets/icons/logo.svg',
              height: 32,
              width: 32,
              placeholderBuilder: (BuildContext context) => SizedBox(
                height: 32,
                width: 32,
                child: const Icon(Icons.image),
              ),
            ),
            const SizedBox(width: 8),
            const Text('Breaking Bright'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () async {
              final settingsChanged = await Navigator.push<bool>(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
              
              if (settingsChanged == true && mounted) {
                setState(() {
                  _selectedIndex = 0; // Reset to home tab
                });
                await _loadNews();
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              showSearch(
                context: context,
                delegate: NewsSearchDelegate(_apiService),
              );
            },
          ),
          IconButton(
            icon: Icon(
              widget.currentThemeMode == ThemeMode.dark
                  ? Icons.light_mode
                  : Icons.dark_mode,
            ),
            onPressed: () {
              widget.setThemeMode(
                widget.currentThemeMode == ThemeMode.dark
                    ? ThemeMode.light
                    : ThemeMode.dark,
              );
            },
          ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite),
            label: 'Favoriten',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.category),
            label: 'Kategorien',
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_selectedIndex == 1) {
      return _buildFavoritesTab();
    } else if (_selectedIndex == 2) {
      return _buildCategoriesTab();
    } else {
      return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_newsData == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Fehler beim Laden der Nachrichten'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadNews,
              child: const Text('Erneut versuchen'),
            ),
          ],
        ),
      );
    }

    if (_newsData!.categories.isEmpty) {
      return const Center(
        child: Text('Keine Nachrichten verfügbar'),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadNews,
      child: ListView.builder(
        itemCount: _newsData!.categories.length,
        itemBuilder: (context, index) {
          return CategoryNewsWidget(
            categoryNews: _newsData!.categories[index],
          );
        },
      ),
    );
  }

  Widget _buildFavoritesTab() {
    return Consumer<FavoritesService>(
      builder: (context, favoritesService, child) {
        final favorites = favoritesService.favorites;
        
        if (favorites.isEmpty) {
          return const Center(
            child: Text('Keine Favoriten gespeichert'),
          );
        }
        
        return ResponsiveNewsGrid(
          newsList: favorites,
          showFavoriteButton: true,
        );
      },
    );
  }

  Widget _buildCategoriesTab() {
    if (_newsData == null || _newsData!.categories.isEmpty) {
      return const Center(
        child: Text('Keine Kategorien verfügbar'),
      );
    }
    
    return ListView.builder(
      itemCount: _newsData!.categories.length,
      itemBuilder: (context, index) {
        final category = _newsData!.categories[index];
        return ListTile(
          leading: SvgPicture.asset(
            'assets/icons/logo.svg',
            height: 24,
            width: 24,
          ),
          title: Text(category.category),
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CategoryViewScreen(
                  categoryCode: category.categoryCode,
                  categoryName: category.category,
                ),
              ),
            );
          },
        );
      },
    );
  }
}
