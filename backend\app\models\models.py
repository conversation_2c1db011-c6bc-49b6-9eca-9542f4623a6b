from sqlalchemy import Column, String, Index, Float, LargeBinary, Integer, CLOB, Boolean, text
from sqlalchemy.dialects.oracle import TIMESTAMP
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class Entry(Base):
    __tablename__ = "entries"
    
    entry_id = Column(String(64), primary_key=True)
    title = Column(String(4000))
    link = Column(String(4000))
    source = Column(String(4000))
    description = Column(CLOB)
    published = Column(TIMESTAMP)
    full_text = Column(CLOB)
    english_text = Column(CLOB)
    embedding = Column(LargeBinary)
    vader_pos = Column(Float)
    vader_neu = Column(Float)
    vader_neg = Column(Float)
    vader_compound = Column(Float)
    german_sentiment_positive = Column(Float)
    german_sentiment_neutral = Column(Float)
    german_sentiment_negative = Column(Float)
    tbd_polarity = Column(Float)
    tbd_subjectivity = Column(Float)
    llm_positive = Column(Float)
    llm_neutral = Column(Float)
    llm_negative = Column(Float)
    llm_positive_std = Column(Float)
    llm_neutral_std = Column(Float)
    llm_negative_std = Column(Float)
    llm_positive_diff = Column(Float)
    llm_neutral_diff = Column(Float)
    llm_negative_diff = Column(Float)
    llm_reason_list = Column(CLOB)
    llm_iterations = Column(Integer)
    llm_break_reason = Column(String(4000))
    llm_is_ad = Column(Float)
    llm_is_ad_reason = Column(CLOB)
    llm_model = Column(String(255))  # New column for LLM model
    llm_provider = Column(String(255))  # New column for LLM provider
    iptc_newscode = Column(String(4000))
    iptc_score = Column(Float)
    dup_entry_id = Column(String(64))
    dup_entry_conf = Column(Float)
    description_auto_generated = Column(Boolean, nullable=True)
    image_prompt = Column(CLOB, nullable=True)
    preview_img = Column(LargeBinary, nullable=True)
    preview_model = Column(String(255))
    preview_provider = Column(String(255))

    # Define table arguments including indices
    __table_args__ = (
        Index('idx_entries_dup_entry_id', 'dup_entry_id'),
        Index('idx_entries_published', text('published DESC')),
        Index('idx_entries_published_asc', 'published'),
        Index('idx_entries_source', 'source'),
        Index('idx_entries_iptc_newscode', 'iptc_newscode'),
        Index('idx_entries_dup_published', 'dup_entry_id', text('published DESC')),
        Index('idx_entries_dup_source', 'dup_entry_id', 'source'),
        Index('idx_entries_dup_source_published', 'dup_entry_id', 'source', text('published DESC'), 'entry_id'),
    )

class Source(Base):
    __tablename__ = "sources"
    
    source = Column(String, nullable=False, primary_key=True)
    name = Column(String)

    __table_args__ = (
        Index('idx_sources_source', 'source'),
    )

class Category(Base):
    __tablename__ = "categories"
    
    iptc_newscode = Column(String, nullable=False, primary_key=True)
    category = Column(String)

    __table_args__ = (
        Index('idx_categories_iptc', 'iptc_newscode'),
    )
