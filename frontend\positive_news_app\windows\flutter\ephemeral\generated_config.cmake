# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\PortableUnsafe\\flutterSdk\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Dokumente\\Private\\Coding\\goodNews\\frontend\\positive_news_app" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\PortableUnsafe\\flutterSdk\\flutter"
  "PROJECT_DIR=C:\\Dokumente\\Private\\Coding\\goodNews\\frontend\\positive_news_app"
  "FLUTTER_ROOT=C:\\PortableUnsafe\\flutterSdk\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Dokumente\\Private\\Coding\\goodNews\\frontend\\positive_news_app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Dokumente\\Private\\Coding\\goodNews\\frontend\\positive_news_app"
  "FLUTTER_TARGET=C:\\Dokumente\\Private\\Coding\\goodNews\\frontend\\positive_news_app\\lib\\main.dart"
  "DART_DEFINES=Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Dokumente\\Private\\Coding\\goodNews\\frontend\\positive_news_app\\.dart_tool\\package_config.json"
)
