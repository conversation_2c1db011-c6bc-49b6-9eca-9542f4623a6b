import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider with ChangeNotifier {
  static const String _lookbackHoursKey = 'lookback_hours';
  static const int defaultLookbackHours = 48;
  
  late SharedPreferences _prefs;
  int _lookbackHours = defaultLookbackHours;

  int get lookbackHours => _lookbackHours;

  SettingsProvider() {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    _prefs = await SharedPreferences.getInstance();
    _lookbackHours = _prefs.getInt(_lookbackHoursKey) ?? defaultLookbackHours;
    notifyListeners();
  }

  Future<void> setLookbackHours(int hours) async {
    if (hours != _lookbackHours) {
      _lookbackHours = hours;
      await _prefs.setInt(_lookbackHoursKey, hours);
      notifyListeners();
    }
  }
}

