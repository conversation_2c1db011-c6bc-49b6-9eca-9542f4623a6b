import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:positive_news_app/models/news_model.dart';
import 'package:positive_news_app/services/api_service.dart';
import 'package:positive_news_app/widgets/responsive_news_grid.dart';

class DetailScreen extends StatefulWidget {
  final EntryWithSource news;

  const DetailScreen({
    super.key,
    required this.news,
  });

  @override
  State<DetailScreen> createState() => _DetailScreenState();
}

class _DetailScreenState extends State<DetailScreen> {
  final ApiService _apiService = ApiService();
  bool _isLoading = true;
  SimilarNewsResponse? _similarNewsResponse;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadSimilarNews();
  }

  Future<void> _loadSimilarNews() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final response = await _apiService.getSimilarNews(widget.news.entryId);
      setState(() {
        _similarNewsResponse = response;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Fehler beim Laden ähnlicher Nachrichten: $e';
        _isLoading = false;
      });
    }
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            SvgPicture.asset(
              'assets/icons/logo.svg',
              height: 28,
              width: 28,
            ),
            const SizedBox(width: 8),
            const Text('Weitere Quellen'),
          ],
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_errorMessage),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSimilarNews,
              child: const Text('Erneut versuchen'),
            ),
          ],
        ),
      );
    }

    if (_similarNewsResponse == null || _similarNewsResponse!.similarSources.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Keine ähnlichen Nachrichten gefunden'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Zurück'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16.0, top: 16.0, right: 16.0, bottom: 6.0),
            child: 
              Text(
                'Original-Nachricht:',
                style: Theme.of(context).textTheme.titleLarge,
              )
          ),
          // Replace the single NewsCard with a ResponsiveNewsGrid containing just the original news
          ResponsiveNewsGrid(
            newsList: [widget.news],
            showFavoriteButton: true,
            showSimilarMessage: false,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 16.0, top: 24.0, right: 16.0, bottom: 6.0),
            child: 
              Text(
                'Ähnliche Nachrichten von anderen Quellen:',
                style: Theme.of(context).textTheme.titleLarge,
              ),
          ),
          ResponsiveNewsGrid(
            newsList: _similarNewsResponse!.similarSources,
            showFavoriteButton: true,
            showSimilarMessage: false,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
          ),
        ],
      ),
    );
  }
}
