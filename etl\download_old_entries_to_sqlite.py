import pandas as pd
import sqlalchemy
from datetime import datetime
import os
import sys

# Ensure project root is in sys.path for config import
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
from backend.app.core.config import settings

# --- CONFIGURATION ---
ORACLE_URL = settings.DATABASE_URL
SQLITE_URL = "sqlite:///goodNewsLocal.sqlite"
CUTOFF_DATE = "2025-04-01"  # YYYY-MM-DD

# --- FETCH FROM ORACLE ---
oracle_engine = sqlalchemy.create_engine(ORACLE_URL)
query = f"SELECT * FROM entries WHERE published < TO_DATE('{CUTOFF_DATE}', 'YYYY-MM-DD')"
df = pd.read_sql(query, oracle_engine)
print(f"Fetched {len(df)} entries from Oracle.")

# --- SAVE TO SQLITE ---
sqlite_engine = sqlalchemy.create_engine(SQLITE_URL)
df.to_sql("entries", sqlite_engine, if_exists="append", index=False)
print(f"Saved {len(df)} entries to local SQLite database.")
