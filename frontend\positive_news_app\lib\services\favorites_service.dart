import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:positive_news_app/models/news_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FavoritesService extends ChangeNotifier {
  List<EntryWithSource> _favorites = []; // Updated to use EntryWithSource
  
  List<EntryWithSource> get favorites => _favorites; // Updated to use EntryWithSource
  
  FavoritesService() {
    _loadFavorites();
  }
  
  Future<void> _loadFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesJson = prefs.getStringList('favorites') ?? [];
    
    _favorites = favoritesJson
        .map((jsonString) => EntryWithSource.fromJson(json.decode(jsonString))) // Updated to use EntryWithSource
        .toList();
    
    notifyListeners();
  }
  
  Future<void> _saveFavorites() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesJson = _favorites
        .map((news) => json.encode(news.toJson())) // Updated to use EntryWithSource
        .toList();
    
    await prefs.setStringList('favorites', favoritesJson);
  }
  
  bool isFavorite(EntryWithSource news) { // Updated to use EntryWithSource
    return _favorites.any((favorite) => favorite.entryId == news.entryId);
  }
  
  void addFavorite(EntryWithSource news) { // Updated to use EntryWithSource
    if (!isFavorite(news)) {
      _favorites.add(news);
      _saveFavorites();
      notifyListeners();
    }
  }
  
  void removeFavorite(EntryWithSource news) { // Updated to use EntryWithSource
    _favorites.removeWhere((favorite) => favorite.entryId == news.entryId);
    _saveFavorites();
    notifyListeners();
  }
  
  void clearFavorites() {
    _favorites.clear();
    _saveFavorites();
    notifyListeners();
  }
}
